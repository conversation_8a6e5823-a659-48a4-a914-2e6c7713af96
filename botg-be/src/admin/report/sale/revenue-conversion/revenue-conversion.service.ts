import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { CrudRequest } from 'src/core/crud/crud';
import { Repository } from 'typeorm';
import { Invoice } from 'src/admin/invoice/invoice.entity';
import { InvoicePaymentMethod } from 'src/admin/invoice/invoice-payment-method.entity';
import { PaymentMethod } from 'src/admin/payment-method/payment-method.entity';
import * as moment from 'moment-timezone';
import { ReportRevenueConversionQueryDTO } from './revenue-conversion.dto';
import { InvoiceStatus, ProductType, CreditType } from 'src/core/enums/entity';
import Decimal from 'decimal.js';
import { OrderDetail } from 'src/admin/order-detail/order-detail.entity';
import { CreditHistory } from 'src/admin/credit-history/credit-history.entity';

@Injectable()
export class ReportRevenueConversionService {
  constructor(
    @InjectRepository(Invoice)
    private invoiceRepo: Repository<Invoice>,
    @InjectRepository(InvoicePaymentMethod)
    private invoicePaymentMethodRepo: Repository<InvoicePaymentMethod>,
    @InjectRepository(PaymentMethod)
    private paymentMethodRepo: Repository<PaymentMethod>,
    @InjectRepository(OrderDetail)
    private orderDetailRepo: Repository<OrderDetail>,
    @InjectRepository(CreditHistory)
    private creditHistoryRepo: Repository<CreditHistory>,
  ) {}

  async getReport(
    req: CrudRequest,
    {
      startDate,
      endDate,
      branchIds,
      clientZoneName,
      currencyCode,
    }: ReportRevenueConversionQueryDTO,
    isExport = false,
  ) {
    const totalSales = await this.getTotalSales(startDate, endDate, branchIds);
    const prepaidSales = await this.getPrepaidSales(
      startDate,
      endDate,
      branchIds,
    );
    const prepaidConsumption = await this.getPrepaidConsumption(
      startDate,
      endDate,
      branchIds,
    );
    const expiredPrepaid = await this.getExpiredPrepaid(
      startDate,
      endDate,
      branchIds,
    );
    const discount = await this.getDiscount(startDate, endDate, branchIds);

    let sumTotalSale = 0;
    let sumPrepaidSale = 0;
    let sumOtherSale = 0;
    let sumPrepaidConsumption = 0;
    let sumExpiredPrepaid = 0;
    let sumDiscount = 0;
    let sumNetRevenue = 0;

    const allDates = new Set([
      ...Object.keys(totalSales),
      ...Object.keys(prepaidSales),
      ...Object.keys(prepaidConsumption),
      ...Object.keys(expiredPrepaid),
      ...Object.keys(discount),
    ]);

    for (const date of allDates) {
      const totalSale = totalSales[date] || 0;
      const prepaidSale = prepaidSales[date] || 0;
      const otherSale = totalSale - prepaidSale;
      const consumption = prepaidConsumption[date] || 0;
      const expired = expiredPrepaid[date] || 0;
      const discountValue = discount[date] || 0;

      // Net Revenue = (A) - (B) + (C) + (E) - (D)
      // A = Total Sale, B = Prepaid Sale, C = Prepaid Consumption, D = Discount, E = Expired Prepaid
      const netRevenue =
        totalSale - prepaidSale + consumption + expired - discountValue;

      sumTotalSale += totalSale;
      sumPrepaidSale += prepaidSale;
      sumOtherSale += otherSale;
      sumPrepaidConsumption += consumption;
      sumExpiredPrepaid += expired;
      sumDiscount += discountValue;
      sumNetRevenue += netRevenue;
    }

    const formattedData = await this.formatResultByDate(
      totalSales,
      prepaidSales,
      prepaidConsumption,
      expiredPrepaid,
      discount,
      clientZoneName,
    );

    if (isExport) {
      return formattedData;
    }

    const dataWithoutGrandTotal = formattedData.slice(0, -1);
    return {
      data: dataWithoutGrandTotal,
      sumTotalSale: sumTotalSale.toFixed(2),
      sumPrepaidSale: sumPrepaidSale.toFixed(2),
      sumOtherSale: sumOtherSale.toFixed(2),
      sumPrepaidConsumption: sumPrepaidConsumption.toFixed(2),
      sumExpiredPrepaid: sumExpiredPrepaid.toFixed(2),
      sumDiscount: sumDiscount.toFixed(2),
      sumNetRevenue: sumNetRevenue.toFixed(2),
    };
  }

  private async getTotalSales(
    startDate: string,
    endDate: string,
    branchIds: string[],
  ) {
    const builderQueryPayment = this.invoiceRepo
      .createQueryBuilder('invoice')
      .leftJoinAndSelect('invoice.invoicePayments', 'invoicePayments')
      .leftJoinAndSelect('invoicePayments.paymentMethod', 'paymentMethod')
      .leftJoinAndSelect('invoice.branch', 'branch')
      .leftJoinAndSelect('invoice.invoiceCoupon', 'invoiceCoupon')
      .where('invoice.date BETWEEN :startDate AND :endDate', {
        startDate,
        endDate,
      })
      .andWhere('invoice.status IN (:...statuses)', {
        statuses: [InvoiceStatus.PAID, InvoiceStatus.PART_PAID],
      });

    if (branchIds && branchIds.length > 0) {
      builderQueryPayment.andWhere('branch.id IN (:...branchIds)', {
        branchIds,
      });
    }

    const invoices = await builderQueryPayment.getMany();
    const salesByDate = {};

    for (const invoice of invoices) {
      const date = moment(invoice.date).format('YYYY-MM-DD');

      if (!salesByDate[date]) {
        salesByDate[date] = 0;
      }

      const subTotal = new Decimal(invoice.subTotal || 0);
      let creditPaid = new Decimal(0);

      if (invoice.invoicePayments && invoice.invoicePayments.length > 0) {
        for (const payment of invoice.invoicePayments) {
          if (
            payment.paymentMethod.code === CreditType.NEW ||
            payment.paymentMethod.code === CreditType.OLD
          ) {
            creditPaid = creditPaid.plus(payment.paid || 0);
          }
        }
      }

      const totalSale = subTotal.minus(creditPaid);
      salesByDate[date] += totalSale.toNumber();
    }

    return salesByDate;
  }

  private async getPrepaidSales(
    startDate: string,
    endDate: string,
    branchIds: string[],
  ) {
    const salesByDate = {};

    const membershipQuery = this.orderDetailRepo
      .createQueryBuilder('orderDetail')
      .leftJoinAndSelect('orderDetail.order', 'order')
      .leftJoinAndSelect('order.invoice', 'invoice')
      .leftJoinAndSelect('orderDetail.product', 'product')
      .leftJoinAndSelect('invoice.branch', 'branch')
      .where('invoice.date BETWEEN :startDate AND :endDate', {
        startDate,
        endDate,
      })
      .andWhere('invoice.status IN (:...statuses)', {
        statuses: [InvoiceStatus.PAID, InvoiceStatus.PART_PAID],
      })
      .andWhere('product.type IN (:...types)', {
        types: [ProductType.MEMBERSHIP],
      });

    if (branchIds && branchIds.length > 0) {
      membershipQuery.andWhere('branch.id IN (:...branchIds)', {
        branchIds,
      });
    }

    const membershipOrderDetails = await membershipQuery.getMany();

    for (const orderDetail of membershipOrderDetails) {
      const date = moment(orderDetail.order.invoice.date).format('YYYY-MM-DD');

      if (!salesByDate[date]) {
        salesByDate[date] = 0;
      }

      const price = new Decimal(orderDetail.price || 0);
      const quantity = new Decimal(orderDetail.quantity || 0);
      const itemTotal = price.times(quantity);

      salesByDate[date] += itemTotal.toNumber();
    }

    const couponQuery = this.orderDetailRepo
      .createQueryBuilder('orderDetail')
      .leftJoinAndSelect('orderDetail.order', 'order')
      .leftJoinAndSelect('order.invoice', 'invoice')
      .leftJoinAndSelect('orderDetail.product', 'product')
      .leftJoinAndSelect('invoice.branch', 'branch')
      .where('invoice.date BETWEEN :startDate AND :endDate', {
        startDate,
        endDate,
      })
      .andWhere('invoice.status IN (:...statuses)', {
        statuses: [InvoiceStatus.PAID, InvoiceStatus.PART_PAID],
      })
      .andWhere('product.type = :couponType', {
        couponType: 'coupon',
      });

    if (branchIds && branchIds.length > 0) {
      couponQuery.andWhere('branch.id IN (:...branchIds)', {
        branchIds,
      });
    }

    const couponOrderDetails = await couponQuery.getMany();

    for (const orderDetail of couponOrderDetails) {
      const date = moment(orderDetail.order.invoice.date).format('YYYY-MM-DD');

      if (!salesByDate[date]) {
        salesByDate[date] = 0;
      }

      const price = new Decimal(orderDetail.price || 0);
      const quantity = new Decimal(orderDetail.quantity || 0);
      const itemTotal = price.times(quantity);

      salesByDate[date] += itemTotal.toNumber();
    }

    return salesByDate;
  }

  private async getPrepaidConsumption(
    startDate: string,
    endDate: string,
    branchIds: string[],
  ) {
    const consumptionByDate = {};

    // Get credit consumption from invoice payments
    const creditQuery = this.invoiceRepo
      .createQueryBuilder('invoice')
      .leftJoinAndSelect('invoice.invoicePayments', 'invoicePayments')
      .leftJoinAndSelect('invoicePayments.paymentMethod', 'paymentMethod')
      .leftJoinAndSelect('invoice.branch', 'branch')
      .where('invoice.date BETWEEN :startDate AND :endDate', {
        startDate,
        endDate,
      })
      .andWhere('invoice.status IN (:...statuses)', {
        statuses: [InvoiceStatus.PAID, InvoiceStatus.PART_PAID],
      })
      .andWhere('paymentMethod.code IN (:...creditCodes)', {
        creditCodes: [CreditType.NEW, CreditType.OLD],
      });

    if (branchIds && branchIds.length > 0) {
      creditQuery.andWhere('branch.id IN (:...branchIds)', {
        branchIds,
      });
    }

    const invoicesWithCredits = await creditQuery.getMany();

    for (const invoice of invoicesWithCredits) {
      const date = moment(invoice.date).format('YYYY-MM-DD');

      if (!consumptionByDate[date]) {
        consumptionByDate[date] = 0;
      }

      for (const payment of invoice.invoicePayments) {
        if (
          payment.paymentMethod.code === CreditType.NEW ||
          payment.paymentMethod.code === CreditType.OLD
        ) {
          const paid = new Decimal(payment.paid || 0);
          consumptionByDate[date] += paid.toNumber();
        }
      }
    }

    // Get coupon redemption from order details with couponCode (legacy flow)
    const couponQuery = this.orderDetailRepo
      .createQueryBuilder('orderDetail')
      .leftJoinAndSelect('orderDetail.order', 'order')
      .leftJoinAndSelect('order.invoice', 'invoice')
      .leftJoinAndSelect('invoice.branch', 'branch')
      .leftJoinAndSelect('orderDetail.product', 'product')
      .where('invoice.date BETWEEN :startDate AND :endDate', {
        startDate,
        endDate,
      })
      .andWhere('invoice.status IN (:...statuses)', {
        statuses: [InvoiceStatus.PAID, InvoiceStatus.PART_PAID],
      })
      .andWhere('orderDetail.couponCode IS NOT NULL')
      .andWhere('orderDetail.couponCode != :emptyString', { emptyString: '' })
      .andWhere('product.type != :productType', {
        productType: 'coupon',
      });

    if (branchIds && branchIds.length > 0) {
      couponQuery.andWhere('branch.id IN (:...branchIds)', {
        branchIds,
      });
    }

    const couponOrderDetails = await couponQuery.getMany();

    for (const orderDetail of couponOrderDetails) {
      const date = moment(orderDetail.order.invoice.date).format('YYYY-MM-DD');

      if (!consumptionByDate[date]) {
        consumptionByDate[date] = 0;
      }

      // For coupon redemption, we need to get the original price value
      const price = new Decimal(orderDetail.price || 0);
      const quantity = new Decimal(orderDetail.quantity || 0);

      consumptionByDate[date] += price.times(quantity).toNumber();
    }

    // Get coupon redemption from InvoiceCoupon table (current flow)
    let invoiceCouponQuery = this.invoiceRepo.manager
      .createQueryBuilder()
      .select([
        'invoiceCoupon.discountValue as discountValue',
        'invoice.date as invoiceDate',
      ])
      .from('invoice_coupon', 'invoiceCoupon')
      .leftJoin(
        'invoice_invoice_coupon_invoice_coupon',
        'junction',
        'junction.invoiceCouponId = invoiceCoupon.id',
      )
      .leftJoin('invoice', 'invoice', 'invoice.id = junction.invoiceId')
      .leftJoin('branch', 'branch', 'branch.id = invoice.branchId')
      .where('invoice.date BETWEEN :startDate AND :endDate', {
        startDate,
        endDate,
      })
      .andWhere('invoice.status IN (:...statuses)', {
        statuses: [InvoiceStatus.PAID, InvoiceStatus.PART_PAID],
      })
      .andWhere('invoiceCoupon.couponCode IS NOT NULL')
      .andWhere('invoiceCoupon.couponCode != :emptyString', { emptyString: '' })
      .andWhere('invoiceCoupon.couponType = :couponType', {
        couponType: 'Code',
      });

    if (branchIds && branchIds.length > 0) {
      invoiceCouponQuery = invoiceCouponQuery.andWhere(
        'branch.id IN (:...branchIds)',
        {
          branchIds,
        },
      );
    }

    const invoiceCoupons = await invoiceCouponQuery.getRawMany();

    for (const coupon of invoiceCoupons) {
      const date = moment(coupon.invoiceDate).format('YYYY-MM-DD');

      if (!consumptionByDate[date]) {
        consumptionByDate[date] = 0;
      }

      // Add the discount value from invoice coupon
      const discountValue = new Decimal(coupon.discountvalue || 0);
      consumptionByDate[date] += discountValue.toNumber();
    }

    return consumptionByDate;
  }

  private async getExpiredPrepaid(
    startDate: string,
    endDate: string,
    branchIds: string[],
  ) {
    const startDateOnly = moment(startDate).format('YYYY-MM-DD');
    const endDateOnly = moment(endDate).format('YYYY-MM-DD');

    console.log('getExpiredPrepaid called with:', {
      startDate,
      endDate,
      startDateOnly,
      endDateOnly,
    });

    const expiredByDate = {};

    if (endDateOnly >= '2025-06-27') {
      const query = this.creditHistoryRepo
        .createQueryBuilder('creditHistory')
        .leftJoinAndSelect('creditHistory.credit', 'credit')
        .leftJoinAndSelect('credit.branch', 'branch')
        .where('creditHistory.isPurgeExpired = :isPurgeExpired', {
          isPurgeExpired: true,
        })
        .andWhere('DATE(creditHistory.created) >= :minDate', {
          minDate: '2025-06-27',
        })
        .andWhere('DATE(creditHistory.created) <= :endDate', {
          endDate: endDateOnly,
        });

      if (branchIds && branchIds.length > 0) {
        query.andWhere('branch.id IN (:...branchIds)', {
          branchIds,
        });
      }

      const expiredCredits = await query.getMany();

      for (const creditHistory of expiredCredits) {
        const date = moment(creditHistory.created).format('YYYY-MM-DD');

        if (!expiredByDate[date]) {
          expiredByDate[date] = 0;
        }

        expiredByDate[date] += creditHistory.paid || 0;
      }
    }

    return expiredByDate;
  }

  private async getDiscount(
    startDate: string,
    endDate: string,
    branchIds: string[],
  ) {
    const query = this.invoiceRepo
      .createQueryBuilder('invoice')
      .leftJoinAndSelect('invoice.branch', 'branch')
      .leftJoinAndSelect('invoice.invoiceCoupon', 'invoiceCoupon')
      .where('invoice.date BETWEEN :startDate AND :endDate', {
        startDate,
        endDate,
      })
      .andWhere('invoice.status IN (:...statuses)', {
        statuses: [InvoiceStatus.PAID, InvoiceStatus.PART_PAID],
      })
      .andWhere('(invoice.discount > 0 OR invoiceCoupon.id IS NOT NULL)');

    if (branchIds && branchIds.length > 0) {
      query.andWhere('branch.id IN (:...branchIds)', {
        branchIds,
      });
    }

    const invoices = await query.getMany();
    const discountByDate = {};

    for (const invoice of invoices) {
      const date = moment(invoice.date).format('YYYY-MM-DD');

      if (!discountByDate[date]) {
        discountByDate[date] = 0;
      }

      const discount = new Decimal(invoice.discount || 0);
      discountByDate[date] += discount.toNumber();
    }

    return discountByDate;
  }

  private async formatResultByDate(
    totalSales,
    prepaidSales,
    prepaidConsumption,
    expiredPrepaid,
    discount,
    clientZoneName,
  ) {
    const allDates = new Set([
      ...Object.keys(totalSales),
      ...Object.keys(prepaidSales),
      ...Object.keys(prepaidConsumption),
      ...Object.keys(expiredPrepaid),
      ...Object.keys(discount),
    ]);

    const result = [];

    let grandTotalSale = 0;
    let grandPrepaidSale = 0;
    let grandOtherSale = 0;
    let grandPrepaidConsumption = 0;
    let grandExpiredPrepaid = 0;
    let grandDiscount = 0;
    let grandNetRevenue = 0;

    for (const date of allDates) {
      const totalSale = totalSales[date] || 0;
      const prepaidSale = prepaidSales[date] || 0;
      const otherSale = totalSale - prepaidSale;
      const consumption = prepaidConsumption[date] || 0;
      const expired = expiredPrepaid[date] || 0;
      const discountValue = discount[date] || 0;

      // Net Revenue = (A) - (B) + (C) + (E) - (D)
      // A = Total Sale, B = Prepaid Sale, C = Prepaid Consumption, D = Discount, E = Expired Prepaid
      const netRevenue =
        totalSale - prepaidSale + consumption + expired - discountValue;

      // Cập nhật tổng
      grandTotalSale += totalSale;
      grandPrepaidSale += prepaidSale;
      grandOtherSale += otherSale;
      grandPrepaidConsumption += consumption;
      grandExpiredPrepaid += expired;
      grandDiscount += discountValue;
      grandNetRevenue += netRevenue;

      const formattedDate = moment(date)
        .tz(clientZoneName)
        .format('DD/MM/YYYY');

      result.push({
        date: formattedDate,
        totalSale: totalSale.toFixed(2),
        prepaidSale: prepaidSale.toFixed(2),
        otherSale: otherSale.toFixed(2),
        prepaidConsumption: consumption.toFixed(2),
        expiredPrepaid: expired.toFixed(2),
        discount: discountValue.toFixed(2),
        netRevenue: netRevenue.toFixed(2),
      });
    }

    // Sắp xếp theo ngày
    result.sort((a, b) =>
      moment(a.date, 'DD/MM/YYYY').diff(moment(b.date, 'DD/MM/YYYY')),
    );

    // Thêm dòng tổng
    result.push({
      date: 'GRAND TOTAL:',
      totalSale: grandTotalSale.toFixed(2),
      prepaidSale: grandPrepaidSale.toFixed(2),
      otherSale: grandOtherSale.toFixed(2),
      prepaidConsumption: grandPrepaidConsumption.toFixed(2),
      expiredPrepaid: grandExpiredPrepaid.toFixed(2),
      discount: grandDiscount.toFixed(2),
      netRevenue: grandNetRevenue.toFixed(2),
    });

    return result;
  }
}
